import 'package:anx_reader/l10n/generated/L10n.dart';
import 'package:anx_reader/page/iap_page.dart';
import 'package:anx_reader/page/settings_page/more_settings_page.dart';
import 'package:anx_reader/service/iap_service.dart';
import 'package:anx_reader/utils/env_var.dart';
import 'package:anx_reader/widgets/settings/about.dart';
import 'package:anx_reader/widgets/settings/theme_mode.dart';
import 'package:anx_reader/widgets/settings/webdav_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            GestureDetector(
              onTap: () {},
              child: Padding(
                padding: const EdgeInsets.fromLTRB(0, 60, 0, 20),
                child: Center(
                  child: Text(
                    'Anx',
                    style: TextStyle(
                      fontSize: 130,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ),
            ),
            const Divider(),
            const Padding(
              padding: EdgeInsets.fromLTRB(20, 8, 10, 8),
              child: ChangeThemeMode(),
            ),
            const Divider(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: webdavSwitch(context, setState, ref),
            ),
            const Divider(),
            const MoreSettings(),
            if (EnvVar.isAppStore)
              ListTile(
                title: Text(L10n.of(context).iap_page_title),
                leading: const Icon(Icons.star_outline),
                subtitle: Text(IAPService().statusTitle(context)),
                onTap: () {
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => const IAPPage()));
              },
            ),
            const About(),
          ],
        ),
      ),
    );
  }
}
